# Story 1.3 修复建议

## 🔧 必须修复的问题

### 1. 数组访问安全问题

#### 问题描述
在`AthleteTableModel::data()`方法中使用`.at()`可能导致异常：

```cpp
// 当前代码 (有风险)
Athlete *athlete = m_athletes.at(index.row());
```

#### 修复方案
```cpp
// 建议修复
Athlete *athlete = nullptr;
if (index.row() >= 0 && index.row() < m_athletes.size()) {
    athlete = m_athletes[index.row()];
} else {
    return QVariant();
}
```

#### 影响文件
- `src/models/athlete_table_model.cpp:52`
- `src/models/athlete_table_model.cpp:378`
- `src/models/athlete_table_model.cpp:387`
- `src/models/athlete_table_model.cpp:399`

### 2. TODO项目完成

#### 问题描述
代码中存在多个未完成的TODO项目：

```cpp
// TODO: Load attempts from database
// TODO: Implement actual database save
```

#### 修复方案
选择以下方案之一：

**方案A: 完成数据库集成**
```cpp
bool AthleteTableModel::loadAttempts()
{
    if (!m_competition || !m_dbManager) {
        return false;
    }
    
    // 实现实际的数据库加载逻辑
    QString query = "SELECT athlete_id, height, attempt_number, result FROM attempts WHERE competition_id = ?";
    // ... 数据库查询实现
}
```

**方案B: 明确标记为未来版本**
```cpp
// NOTE: Database integration planned for v1.1
// Currently using in-memory storage only
void AthleteTableModel::loadAttempts()
{
    // In-memory initialization for v1.0
    // Database integration will be added in v1.1
}
```

### 3. 死锁风险修复

#### 问题描述
在`CompetitionState::advanceToNextHeight()`中存在潜在死锁：

```cpp
// 当前代码 (有死锁风险)
void CompetitionState::advanceToNextHeight() {
    // ...
    setCurrentHeight(nextHeight); // 获取锁1
    QMutexLocker locker(&m_stateMutex); // 再次获取同一个锁
    m_currentRound++;
}
```

#### 修复方案
```cpp
// 修复方案A: 重构为私有无锁方法
void CompetitionState::advanceToNextHeight() {
    QMutexLocker locker(&m_stateMutex);

    if (!m_competition) {
        return false;
    }

    QList<int> heights = m_competition->getHeightProgression();
    int currentIndex = heights.indexOf(m_currentHeight);

    if (currentIndex < 0 || currentIndex >= heights.size() - 1) {
        if (shouldFinishCompetitionLocked()) {
            emit competitionShouldFinish();
        }
        return false;
    }

    int nextHeight = heights[currentIndex + 1];
    setCurrentHeightLocked(nextHeight); // 私有无锁版本
    m_currentRound++;

    return true;
}

private:
void setCurrentHeightLocked(int height) {
    // 无锁版本，假设调用者已持有锁
    if (m_currentHeight == height) {
        return;
    }

    m_currentHeight = height;
    m_lastUpdateTime = QDateTime::currentDateTime();

    // 注意：信号发射需要在锁外进行
    QMetaObject::invokeMethod(this, [this, height]() {
        emit currentHeightChanged(height);
    }, Qt::QueuedConnection);
}
```

## 🔄 建议修复的问题

### 1. 性能测试基准调整

#### 当前问题
性能测试阈值可能过于宽松：

```cpp
static const int MAX_LOADING_TIME_MS = 1000;      // 可能过于宽松
static const int MAX_RANKING_TIME_MS = 500;       // 可能过于宽松
```

#### 建议修复
```cpp
// 根据实际硬件调整基准
static const int MAX_LOADING_TIME_MS = 800;       // 更严格的标准
static const int MAX_RANKING_TIME_MS = 300;       // 更严格的标准
static const int MAX_RENDER_TIME_MS = 50;         // 更严格的标准
```

### 2. 国际化支持完善

#### 当前问题
部分字符串未使用tr()函数：

```cpp
// 当前代码
m_statusLabel->setText("就绪");
```

#### 建议修复
```cpp
// 修复后
m_statusLabel->setText(tr("Ready"));
```

## 🎨 可选改进建议

### 1. UI常量配置化

#### 当前代码
```cpp
static const int CELL_PADDING = 4;
static const int MIN_CELL_WIDTH = 60;
static const int MIN_CELL_HEIGHT = 24;
```

#### 建议改进
```cpp
// 从配置文件读取
class UIConstants {
public:
    static int cellPadding() { return ConfigManager::instance()->getValue("ui.cell_padding", 4).toInt(); }
    static int minCellWidth() { return ConfigManager::instance()->getValue("ui.min_cell_width", 60).toInt(); }
    static int minCellHeight() { return ConfigManager::instance()->getValue("ui.min_cell_height", 24).toInt(); }
};
```

### 2. 错误处理增强

#### 当前代码
```cpp
if (!m_competition) {
    return;
}
```

#### 建议改进
```cpp
if (!m_competition) {
    qWarning() << "AthleteTableModel: No competition loaded";
    emit errorOccurred(tr("No competition data available"));
    return;
}
```

### 3. 内存使用监控

#### 建议添加
```cpp
class MemoryMonitor {
public:
    static void logMemoryUsage(const QString &context) {
        qint64 memory = getCurrentMemoryUsage();
        if (memory > WARNING_THRESHOLD) {
            qWarning() << "High memory usage detected:" << memory << "bytes in" << context;
        }
    }
};
```

## 🧪 测试改进建议

### 1. 增加边界条件测试

```cpp
void TestAthleteTableModel::testBoundaryConditions()
{
    // 测试空数据
    m_model->loadCompetition(nullptr);
    QCOMPARE(m_model->rowCount(), 0);
    
    // 测试大量数据
    createLargeDataset(1000);
    m_model->loadCompetition(m_competition);
    QVERIFY(m_model->rowCount() == 1000);
    
    // 测试无效索引访问
    QModelIndex invalidIndex = m_model->index(-1, -1);
    QVERIFY(!invalidIndex.isValid());
}
```

### 2. 增加并发测试

```cpp
void TestAthleteTableModel::testConcurrentAccess()
{
    m_model->loadCompetition(m_competition);
    
    // 模拟多线程访问
    QThreadPool pool;
    for (int i = 0; i < 10; ++i) {
        pool.start([this, i]() {
            m_model->recordAttempt(i % 5 + 1, 150, 1, JumpAttempt::Pass);
        });
    }
    
    pool.waitForDone();
    // 验证数据一致性
}
```

## 📋 修复优先级

### 🔴 高优先级 (本周内完成)
1. 数组访问安全问题修复
2. 死锁风险修复
3. TODO项目状态明确

### 🟡 中优先级 (下个迭代)
1. 性能测试基准调整
2. 国际化支持完善
3. 错误处理增强

### 🟢 低优先级 (未来版本)
1. UI常量配置化
2. 内存使用监控
3. 自动化UI测试

## 🎯 修复验证清单

### 代码修复验证
- [ ] 所有`.at()`调用已替换为安全访问
- [ ] TODO项目已完成或明确标记
- [ ] 新增的错误处理已测试
- [ ] 国际化字符串已更新

### 测试验证
- [ ] 所有单元测试通过
- [ ] 新增的边界条件测试通过
- [ ] 性能测试符合新基准
- [ ] 内存泄漏测试通过

### 文档更新
- [ ] API文档已更新
- [ ] 修复说明已记录
- [ ] 版本变更日志已更新

## 🚀 发布准备

修复完成后，执行以下步骤：

1. **代码审查**: 重新进行代码审查
2. **完整测试**: 运行所有测试套件
3. **性能验证**: 确认性能指标达标
4. **文档更新**: 更新相关文档
5. **发布准备**: 准备发布说明

---

**修复负责人**: 开发团队  
**预计完成时间**: 2-3个工作日  
**QA重新审查**: 修复完成后
