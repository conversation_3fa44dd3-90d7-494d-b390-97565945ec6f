#include "scoring_view.h"
#include "models/athlete_table_model.h"
#include "ui/athlete_delegate.h"
#include "ui/shortcut_manager.h"
#include "models/competition.h"
#include "models/jump_attempt.h"
#include <QResizeEvent>
#include <QKeyEvent>
#include <QHeaderView>
#include <QDateTime>
#include <QMessageBox>
#include <QApplication>
#include <QDebug>

ScoringView::ScoringView(QWidget *parent)
    : QWidget(parent)
    , m_tableModel(new AthleteTableModel(this))
    , m_tableDelegate(new AthleteDelegate(this))
    , m_shortcutManager(new ShortcutManager(this))
    , m_tableView(new QTableView(this))
    , m_mainLayout(new QVBoxLayout(this))
    , m_contentLayout(new QHBoxLayout())
    , m_mainSplitter(new QSplitter(Qt::Horizontal, this))
    , m_statusGroup(new QGroupBox(tr("比赛状态"), this))
    , m_statusLayout(new QGridLayout(m_statusGroup))
    , m_competitionNameLabel(new QLabel(tr("未加载比赛"), this))
    , m_currentHeightLabel(new QLabel(tr("当前高度: --"), this))
    , m_activeAthletesLabel(new QLabel(tr("活跃运动员: 0"), this))
    , m_completedAthletesLabel(new QLabel(tr("完成运动员: 0"), this))
    , m_progressBar(new QProgressBar(this))
    , m_nextHeightButton(new QPushButton(tr("下一高度"), this))
    , m_toolBar(new QToolBar(this))
    , m_refreshButton(new QPushButton(tr("刷新"), this))
    , m_exportButton(new QPushButton(tr("导出"), this))
    , m_settingsButton(new QPushButton(tr("设置"), this))
    , m_statusBar(new QStatusBar(this))
    , m_statusLabel(new QLabel(tr("就绪"), this))
    , m_timeLabel(new QLabel(this))
    , m_competition(nullptr)
    , m_currentHeight(0)
    , m_statusUpdateTimer(new QTimer(this))
    , m_timeUpdateTimer(new QTimer(this))
{
    setupUI();
    setupConnections();

    // Start timers
    m_statusUpdateTimer->start(1000); // Update every second
    m_timeUpdateTimer->start(1000);   // Update time every second

    qDebug() << "ScoringView: Initialized with AthleteTableModel and AthleteDelegate";
}

ScoringView::~ScoringView()
{
    qDebug() << "ScoringView: Destroyed";
}

void ScoringView::setupUI()
{
    setMinimumSize(1000, 700);

    // Setup components
    setupTableView();
    setupStatusPanel();
    setupToolBar();
    setupStatusBar();
    setupLayout();

    qDebug() << "ScoringView::setupUI: UI components initialized";
}

void ScoringView::setupTableView()
{
    // Configure table view
    m_tableView->setModel(m_tableModel);
    m_tableView->setItemDelegate(m_tableDelegate);

    // Set delegate's model reference
    m_tableDelegate->setAthleteTableModel(m_tableModel);

    // Configure shortcut manager
    m_shortcutManager->setTableView(m_tableView);
    m_shortcutManager->setTableModel(m_tableModel);

    // Configure table properties
    m_tableView->setSelectionBehavior(QAbstractItemView::SelectItems);
    m_tableView->setSelectionMode(QAbstractItemView::SingleSelection);
    m_tableView->setAlternatingRowColors(true);
    m_tableView->setSortingEnabled(false);
    m_tableView->setShowGrid(true);
    m_tableView->setGridStyle(Qt::SolidLine);

    // Configure headers
    QHeaderView *horizontalHeader = m_tableView->horizontalHeader();
    horizontalHeader->setStretchLastSection(false);
    horizontalHeader->setSectionResizeMode(QHeaderView::Interactive);
    horizontalHeader->setMinimumSectionSize(60);
    horizontalHeader->setDefaultSectionSize(80);

    QHeaderView *verticalHeader = m_tableView->verticalHeader();
    verticalHeader->setVisible(false);
    verticalHeader->setDefaultSectionSize(30);

    // Set minimum size
    m_tableView->setMinimumSize(600, 400);

    qDebug() << "ScoringView::setupTableView: Table view configured";
}

void ScoringView::setupStatusPanel()
{
    // Configure status group
    m_statusGroup->setMaximumWidth(300);
    m_statusGroup->setMinimumWidth(250);

    // Add status labels to grid layout
    m_statusLayout->addWidget(new QLabel(tr("比赛名称:")), 0, 0);
    m_statusLayout->addWidget(m_competitionNameLabel, 0, 1);

    m_statusLayout->addWidget(new QLabel(tr("当前高度:")), 1, 0);
    m_statusLayout->addWidget(m_currentHeightLabel, 1, 1);

    m_statusLayout->addWidget(new QLabel(tr("活跃运动员:")), 2, 0);
    m_statusLayout->addWidget(m_activeAthletesLabel, 2, 1);

    m_statusLayout->addWidget(new QLabel(tr("完成运动员:")), 3, 0);
    m_statusLayout->addWidget(m_completedAthletesLabel, 3, 1);

    m_statusLayout->addWidget(new QLabel(tr("比赛进度:")), 4, 0);
    m_statusLayout->addWidget(m_progressBar, 4, 1);

    m_statusLayout->addWidget(m_nextHeightButton, 5, 0, 1, 2);

    // Configure progress bar
    m_progressBar->setRange(0, 100);
    m_progressBar->setValue(0);

    // Configure next height button
    m_nextHeightButton->setEnabled(false);

    qDebug() << "ScoringView::setupStatusPanel: Status panel configured";
}

void ScoringView::setupToolBar()
{
    // Add buttons to toolbar
    m_toolBar->addWidget(m_refreshButton);
    m_toolBar->addSeparator();
    m_toolBar->addWidget(m_exportButton);
    m_toolBar->addSeparator();
    m_toolBar->addWidget(m_settingsButton);

    // Configure buttons
    m_refreshButton->setToolTip(tr("刷新数据"));
    m_exportButton->setToolTip(tr("导出结果"));
    m_settingsButton->setToolTip(tr("设置"));

    // Initially disable export button
    m_exportButton->setEnabled(false);

    qDebug() << "ScoringView::setupToolBar: Toolbar configured";
}

void ScoringView::setupStatusBar()
{
    // Add labels to status bar
    m_statusBar->addWidget(m_statusLabel);
    m_statusBar->addPermanentWidget(m_timeLabel);

    // Update time display
    updateStatusDisplay();

    qDebug() << "ScoringView::setupStatusBar: Status bar configured";
}

void ScoringView::setupLayout()
{
    // Setup content layout (table + status panel)
    m_contentLayout->addWidget(m_tableView, 3); // 75% width
    m_contentLayout->addWidget(m_statusGroup, 1); // 25% width

    // Setup main layout
    m_mainLayout->addWidget(m_toolBar);
    m_mainLayout->addLayout(m_contentLayout);
    m_mainLayout->addWidget(m_statusBar);

    // Set layout margins
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(5);

    setLayout(m_mainLayout);

    qDebug() << "ScoringView::setupLayout: Layout configured";
}

void ScoringView::setupConnections()
{
    // Connect table model signals
    connect(m_tableModel, &AthleteTableModel::athleteDataUpdated,
            this, &ScoringView::onRankingsUpdated);
    connect(m_tableModel, &AthleteTableModel::rankingsUpdated,
            this, &ScoringView::onRankingsUpdated);
    connect(m_tableModel, &AthleteTableModel::attemptRecorded,
            this, &ScoringView::onAttemptRequested);

    // Connect delegate signals
    connect(m_tableDelegate, &AthleteDelegate::attemptRequested,
            this, &ScoringView::onAttemptRequested);
    connect(m_tableDelegate, &AthleteDelegate::cellFocusChanged,
            this, [this](int athleteId, int height) {
                Q_UNUSED(athleteId)
                Q_UNUSED(height)
                // Handle cell focus changes if needed
            });

    // Connect shortcut manager signals
    connect(m_shortcutManager, QOverload<int, int, int, ShortcutManager::AttemptResult>::of(&ShortcutManager::attemptRecordRequested),
            this, [this](int athleteId, int height, int attemptNumber, ShortcutManager::AttemptResult result) {
                // Convert ShortcutManager::AttemptResult to delegate result
                int delegateResult = static_cast<int>(result);
                onAttemptRequested(athleteId, height, attemptNumber, delegateResult);
            });
    connect(m_shortcutManager, &ShortcutManager::undoRedoStateChanged,
            this, [this](bool canUndo, bool canRedo) {
                // Update UI state based on undo/redo availability
                // TODO: Add undo/redo buttons to toolbar
                Q_UNUSED(canUndo)
                Q_UNUSED(canRedo)
            });

    // Connect button signals
    connect(m_refreshButton, &QPushButton::clicked,
            this, &ScoringView::refreshDisplay);
    connect(m_nextHeightButton, &QPushButton::clicked,
            this, &ScoringView::onNextHeight);
    connect(m_exportButton, &QPushButton::clicked,
            this, [this]() {
                // TODO: Implement export functionality
                QMessageBox::information(this, tr("导出"), tr("导出功能将在后续版本中实现"));
            });
    connect(m_settingsButton, &QPushButton::clicked,
            this, [this]() {
                // TODO: Implement settings dialog
                QMessageBox::information(this, tr("设置"), tr("设置功能将在后续版本中实现"));
            });

    // Connect timers
    connect(m_statusUpdateTimer, &QTimer::timeout,
            this, &ScoringView::onStatusUpdateTimer);
    connect(m_timeUpdateTimer, &QTimer::timeout,
            this, &ScoringView::updateStatusDisplay);

    qDebug() << "ScoringView::setupConnections: Signal connections established";
}

// Public interface methods
void ScoringView::loadCompetition(Competition *competition)
{
    if (!competition) {
        qWarning() << "ScoringView::loadCompetition: Null competition provided";
        return;
    }

    qDebug() << "ScoringView::loadCompetition: Loading competition" << competition->name();

    m_competition = competition;

    // Load competition into table model
    m_tableModel->loadCompetition(competition);

    // Update UI
    m_competitionNameLabel->setText(competition->name());

    // Set initial height
    QList<int> heights = competition->getHeightProgression();
    if (!heights.isEmpty()) {
        setCurrentHeight(heights.first());
    }

    // Enable controls
    m_nextHeightButton->setEnabled(true);
    m_exportButton->setEnabled(true);

    // Update status
    updateCompetitionProgress();

    qDebug() << "ScoringView::loadCompetition: Competition loaded successfully";
}

Competition* ScoringView::getCurrentCompetition() const
{
    return m_competition;
}

void ScoringView::clearCompetition()
{
    qDebug() << "ScoringView::clearCompetition: Clearing competition data";

    m_competition = nullptr;
    m_currentHeight = 0;

    // Clear table model
    m_tableModel->clearData();

    // Reset UI
    m_competitionNameLabel->setText(tr("未加载比赛"));
    m_currentHeightLabel->setText(tr("当前高度: --"));
    m_activeAthletesLabel->setText(tr("活跃运动员: 0"));
    m_completedAthletesLabel->setText(tr("完成运动员: 0"));
    m_progressBar->setValue(0);

    // Disable controls
    m_nextHeightButton->setEnabled(false);
    m_exportButton->setEnabled(false);

    qDebug() << "ScoringView::clearCompetition: Competition data cleared";
}

QTableView* ScoringView::getAthleteTableView() const
{
    return m_tableView;
}

AthleteTableModel* ScoringView::getAthleteTableModel() const
{
    return m_tableModel;
}

void ScoringView::setCurrentHeight(int height)
{
    if (height == m_currentHeight) {
        return;
    }

    m_currentHeight = height;
    m_currentHeightLabel->setText(tr("当前高度: %1cm").arg(height));

    qDebug() << "ScoringView::setCurrentHeight: Height set to" << height;

    emit competitionStateChanged(height, getActiveAthleteCount());
}

int ScoringView::getCurrentHeight() const
{
    return m_currentHeight;
}

void ScoringView::updateCompetitionProgress()
{
    if (!m_competition) {
        return;
    }

    int totalAthletes = m_competition->getAthletes().size();
    int activeAthletes = getActiveAthleteCount();
    int completedAthletes = totalAthletes - activeAthletes;

    // Update labels
    m_activeAthletesLabel->setText(tr("活跃运动员: %1").arg(activeAthletes));
    m_completedAthletesLabel->setText(tr("完成运动员: %1").arg(completedAthletes));

    // Update progress bar
    if (totalAthletes > 0) {
        int progress = (completedAthletes * 100) / totalAthletes;
        m_progressBar->setValue(progress);
    }

    // Check if competition is finished
    if (activeAthletes == 0 && totalAthletes > 0) {
        emit competitionFinished();
    }
}

// Slot implementations
void ScoringView::onAttemptRequested(int athleteId, int height, int attemptNumber, int result)
{
    if (!m_tableModel) {
        return;
    }

    qDebug() << "ScoringView::onAttemptRequested: Recording attempt for athlete"
             << athleteId << "at height" << height << "attempt" << attemptNumber << "result" << result;

    // Convert delegate result to JumpAttempt::AttemptResult
    JumpAttempt::AttemptResult jumpResult;
    switch (result) {
    case 0: // Success
        jumpResult = JumpAttempt::Pass;
        break;
    case 1: // Failure
        jumpResult = JumpAttempt::Fail;
        break;
    case 2: // Skip
        jumpResult = JumpAttempt::Skip;
        break;
    case 3: // Retire
        jumpResult = JumpAttempt::Retire;
        break;
    default:
        jumpResult = JumpAttempt::Invalid;
        break;
    }

    // Record attempt in model
    bool success = m_tableModel->recordAttempt(athleteId, height, attemptNumber, jumpResult);

    if (success) {
        emit attemptRecorded(athleteId, height, attemptNumber, result);
        updateCompetitionProgress();
    } else {
        qWarning() << "ScoringView::onAttemptRequested: Failed to record attempt";
    }
}

void ScoringView::onRankingsUpdated()
{
    updateCompetitionProgress();
    qDebug() << "ScoringView::onRankingsUpdated: Rankings updated";
}

void ScoringView::onCompetitionProgressChanged()
{
    updateCompetitionProgress();
}

void ScoringView::onNextHeight()
{
    if (!m_competition) {
        return;
    }

    QList<int> heights = m_competition->getHeightProgression();
    int currentIndex = heights.indexOf(m_currentHeight);

    if (currentIndex >= 0 && currentIndex < heights.size() - 1) {
        int nextHeight = heights[currentIndex + 1];
        setCurrentHeight(nextHeight);

        // TODO: Add height to competition if not already present
        m_tableModel->addHeight(nextHeight);

        qDebug() << "ScoringView::onNextHeight: Advanced to height" << nextHeight;
    } else {
        QMessageBox::information(this, tr("比赛进度"), tr("已达到最高高度"));
    }
}

void ScoringView::refreshDisplay()
{
    if (m_tableModel) {
        m_tableModel->refreshData();
    }
    updateCompetitionProgress();
    updateStatusDisplay();

    qDebug() << "ScoringView::refreshDisplay: Display refreshed";
}

// Event handlers
void ScoringView::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    adjustLayoutForSize();
}

void ScoringView::keyPressEvent(QKeyEvent *event)
{
    // Let ShortcutManager handle most keyboard events
    // Only handle events that ShortcutManager doesn't cover
    QWidget::keyPressEvent(event);
}

// Private slot implementations
void ScoringView::onStatusUpdateTimer()
{
    updateCompetitionProgress();
}

// Helper methods
void ScoringView::updateStatusDisplay()
{
    // Update time display
    QDateTime currentTime = QDateTime::currentDateTime();
    m_timeLabel->setText(currentTime.toString("hh:mm:ss"));

    // Update status based on competition state
    if (!m_competition) {
        m_statusLabel->setText(tr("就绪 - 未加载比赛"));
    } else if (isCompetitionActive()) {
        m_statusLabel->setText(tr("比赛进行中"));
    } else {
        m_statusLabel->setText(tr("比赛已结束"));
    }
}

void ScoringView::updateProgressBar()
{
    updateCompetitionProgress();
}

void ScoringView::adjustLayoutForSize()
{
    // Responsive layout adjustments based on window size
    int totalWidth = width();

    if (totalWidth < 1000) {
        // Small screen: adjust status panel width
        m_statusGroup->setMaximumWidth(200);
    } else {
        // Large screen: normal status panel width
        m_statusGroup->setMaximumWidth(300);
    }

    qDebug() << "ScoringView::adjustLayoutForSize: Adjusted for width" << totalWidth;
}

bool ScoringView::isCompetitionActive() const
{
    return m_competition && getActiveAthleteCount() > 0;
}

int ScoringView::getActiveAthleteCount() const
{
    if (!m_competition) {
        return 0;
    }

    // TODO: Implement logic to count active athletes
    // For now, return total athlete count
    return m_competition->getAthletes().size();
}

